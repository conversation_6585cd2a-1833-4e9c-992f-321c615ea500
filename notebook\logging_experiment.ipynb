{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d4600b0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all ok\n"]}], "source": ["print(\"all ok\")"]}, {"cell_type": "code", "execution_count": 2, "id": "a038e03d", "metadata": {}, "outputs": [], "source": ["import logging\n", "import os\n", "from datetime import datetime"]}, {"cell_type": "code", "execution_count": 3, "id": "67a48723", "metadata": {}, "outputs": [{"data": {"text/plain": ["'c:\\\\Users\\\\<USER>\\\\document_portal\\\\notebook'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getcwd()"]}, {"cell_type": "code", "execution_count": 4, "id": "7499cf7a", "metadata": {}, "outputs": [], "source": ["logs_dir=os.path.join(os.getcwd(),\"logs\")\n", "os.makedirs(logs_dir, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "9a8b2dcb", "metadata": {}, "outputs": [], "source": ["LOG_FILE = f\"{datetime.now().strftime('%m_%d_%Y_%H_%M_%S')}.log\""]}, {"cell_type": "code", "execution_count": 6, "id": "2c05e24c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'07_26_2025_16_27_47.log'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["#this is a log file(name of the log file would current data and time)\n", "LOG_FILE"]}, {"cell_type": "code", "execution_count": 7, "id": "f4cac1a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'07_26_2025_16_27_47.log'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["LOG_FILE"]}, {"cell_type": "code", "execution_count": 8, "id": "cab3a7e0", "metadata": {}, "outputs": [], "source": ["#today\n", "# with today's date log file will be creted\n", "\n", "#tomorrow\n", "# with tomorrow's date log file will be created"]}, {"cell_type": "code", "execution_count": 9, "id": "07745454", "metadata": {}, "outputs": [], "source": ["LOG_FILE_PATH=os.path.join(logs_dir, LOG_FILE)"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "19dd7e0e", "metadata": {}, "source": ["![image.png](attachment:image.png)"]}, {"cell_type": "code", "execution_count": 10, "id": "339a8e71", "metadata": {}, "outputs": [], "source": ["logging.basicConfig(\n", "    filename=LOG_FILE_PATH,\n", "    format=\"[ %(asctime)s ] %(levelname)s %(name)s (line:%(lineno)d) - %(message)s\",\n", "    level=logging.INFO,\n", ")"]}, {"cell_type": "markdown", "id": "91963caf", "metadata": {}, "source": ["[ 2025-07-26 14:47:37,813 ] INFO DocumentPortal (line:1) - This is an info message\n"]}, {"cell_type": "code", "execution_count": 11, "id": "333f1ea7", "metadata": {}, "outputs": [], "source": ["logger=logging.getLogger(\"DocumentPortal\")"]}, {"cell_type": "code", "execution_count": 12, "id": "3c95d206", "metadata": {}, "outputs": [], "source": ["logger.info(\"This is an info message\")"]}, {"cell_type": "code", "execution_count": 13, "id": "349fe6ce", "metadata": {}, "outputs": [], "source": ["## python concept or python basics shoule clear"]}, {"cell_type": "code", "execution_count": 14, "id": "471107ff", "metadata": {}, "outputs": [], "source": ["import structlog\n", "log=structlog.get_logger()"]}, {"cell_type": "code", "execution_count": 15, "id": "a1e0fb2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-26 16:28:13 [info     ] User uploaded a file           filename=report.pdf user_id=123\n"]}], "source": ["log.info(\"User uploaded a file\", user_id=123, filename=\"report.pdf\")"]}, {"cell_type": "code", "execution_count": 16, "id": "52495ccb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-07-26 16:28:15 [error    ] Failed to process PDF          error='File not found' user_id=123\n"]}], "source": ["log.error(\"Failed to process PDF\", error=\"File not found\", user_id=123)"]}, {"cell_type": "code", "execution_count": null, "id": "95263639", "metadata": {}, "outputs": [], "source": ["[ 2025-07-26 13:34:22,657 ] INFO __main__ (line:5) - {\"user_id\": 123, \"filename\": \"report.pdf\", \"event\": \"User uploaded a file\", \"timestamp\": \"2025-07-26T08:04:22.657768Z\"}\n", "[ 2025-07-26 13:34:22,658 ] ERROR __main__ (line:6) - {\"error\": \"File not found\", \"user_id\": 123, \"event\": \"Failed to process PDF\", \"timestamp\": \"2025-07-26T08:04:22.658768Z\"}\n"]}, {"cell_type": "code", "execution_count": null, "id": "6628604f", "metadata": {}, "outputs": [], "source": ["AWS Repo(ELK)\n", "ECS LOGS\n", "Cloud Watch"]}, {"cell_type": "code", "execution_count": 1, "id": "7bb3e014", "metadata": {}, "outputs": [], "source": ["class MyClass:\n", "    def __init__(self, name):\n", "        self.name = name\n", "    \n", "    def __str__(self):\n", "        return f\"MyClass object with name: {self.name}\"\n", "    \n", "    def __repr__(self):\n", "        return f\"MyClass object with name: {self.name}\""]}, {"cell_type": "code", "execution_count": 2, "id": "90c457e9", "metadata": {}, "outputs": [], "source": ["obj=MyClass(\"sunny\")"]}, {"cell_type": "code", "execution_count": null, "id": "8418aa34", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}