{"cells": [{"cell_type": "code", "execution_count": 1, "id": "af29cdef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["all ok\n"]}], "source": ["print(\"all ok\")"]}, {"cell_type": "code", "execution_count": null, "id": "3951ffa8", "metadata": {}, "outputs": [], "source": ["### __file__ in .py file automatically gets the name of the file"]}, {"cell_type": "code", "execution_count": 9, "id": "dd965647", "metadata": {}, "outputs": [], "source": ["from logger.custom_logger import CustomLogger"]}, {"cell_type": "code", "execution_count": 11, "id": "b6c866fb", "metadata": {}, "outputs": [], "source": ["logger=CustomLogger().get_logger(\"exception_experiment\")"]}, {"cell_type": "code", "execution_count": 6, "id": "312d87ea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(<class 'ZeroDivisionError'>, ZeroDivisionError('division by zero'), <traceback object at 0x00000231341AAD40>)\n"]}, {"ename": "NameError", "evalue": "name 'logger' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mZeroDivisionError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 23\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m     22\u001b[0m     \u001b[38;5;66;03m# Simulate an error\u001b[39;00m\n\u001b[1;32m---> 23\u001b[0m     a \u001b[38;5;241m=\u001b[39m \u001b[38;5;241;43m1\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m/\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\n\u001b[0;32m     24\u001b[0m     \u001b[38;5;28mprint\u001b[39m(a)\n", "\u001b[1;31mZeroDivisionError\u001b[0m: division by zero", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[6], line 27\u001b[0m\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m     26\u001b[0m     app_exc\u001b[38;5;241m=\u001b[39mDocumentPortalException(e,sys)\n\u001b[1;32m---> 27\u001b[0m     \u001b[43mlogger\u001b[49m\u001b[38;5;241m.\u001b[39merror(app_exc)\n\u001b[0;32m     28\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m app_exc\n", "\u001b[1;31mNameError\u001b[0m: name 'logger' is not defined"]}], "source": ["import sys\n", "import traceback\n", "class DocumentPortalException(Exception):\n", "    \"\"\"Custom exception for Document Portal\"\"\"\n", "    def __init__(self,error_message,error_details:sys):\n", "        print(error_details.exc_info())\n", "        _,_,exc_tb=error_details.exc_info()\n", "        self.file_name=exc_tb.tb_frame.f_code.co_filename\n", "        self.lineno=exc_tb.tb_lineno\n", "        self.error_message=str(error_message)\n", "        self.traceback_str = ''.join(traceback.format_exception(*error_details.exc_info())) \n", "    def __str__(self):\n", "       return f\"\"\"\n", "        Error in [{self.file_name}] at line [{self.lineno}]\n", "        Message: {self.error_message}\n", "        Traceback:\n", "        {self.traceback_str}\n", "        \"\"\"\n", "    \n", "if __name__ == \"__main__\":\n", "    try:\n", "        # Simulate an error\n", "        a = 1 / 0\n", "        print(a)\n", "    except Exception as e:\n", "        app_exc=DocumentPortalException(e,sys)\n", "        logger.error(app_exc)\n", "        raise app_exc"]}, {"cell_type": "code", "execution_count": null, "id": "14522cf0", "metadata": {}, "outputs": [], "source": ["1. log this error(file, cloud config, command prompt) (it will keep the record of the error and will not stop the execution of the code)\n", "2. raise the exception(it will stop the execution of the code)"]}, {"cell_type": "code", "execution_count": null, "id": "ceb5545d", "metadata": {}, "outputs": [], "source": ["(<class 'ZeroDivisionError'>, ZeroDivisionError('division by zero'), <traceback object at 0x00000231341AAD40>)"]}, {"cell_type": "code", "execution_count": 1, "id": "2fa1a218", "metadata": {}, "outputs": [{"data": {"text/plain": ["(None, None, None)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.exc_info()"]}, {"cell_type": "code", "execution_count": 2, "id": "7ffcd5ce", "metadata": {}, "outputs": [], "source": ["a,b,c=(1,2,3)"]}, {"cell_type": "code", "execution_count": 3, "id": "300ac63a", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 4, "id": "ad3f4313", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["b"]}, {"cell_type": "code", "execution_count": 5, "id": "eebf47e7", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["c"]}, {"cell_type": "code", "execution_count": null, "id": "ff4039d7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}